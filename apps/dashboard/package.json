{"dependencies": {"@legendapp/state": "^3.0.0-beta.31", "@mass/api": "workspace:*", "@mass/components": "workspace:*", "@mass/icons": "workspace:*", "@mass/tailwind": "workspace:*", "@mass/utils": "workspace:*", "@mass/assets": "workspace:*", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.127.3", "@tanstack/react-router-devtools": "^1.127.3", "@tanstack/router-plugin": "^1.127.5", "@headlessui/react": "^2.2.4", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-pdf": "^10.0.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "tailwindcss": "^4.1.11", "react-hot-toast": "^2.5.2", "arktype": "^2.1.20", "dayjs": "^1.11.13", "react-day-picker": "^9.8.0", "@tanstack/react-table": "^8.21.3"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "globals": "^16.3.0", "serve": "^14.2.4", "typescript": "~5.8.3", "vite": "^7.0.4", "vite-plugin-static-copy": "^3.1.1"}, "name": "dashboard", "private": true, "type": "module", "version": "0.0.0"}